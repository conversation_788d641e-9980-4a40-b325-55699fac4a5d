from odoo import models, fields


class HrAttendanceDevice(models.Model):
    _name = 'hr.attendance.device'
    _description = 'Dispositivo de fichado'

    name = fields.Char(string="Nombre del equipo", required=True)
    project_id = fields.Many2one('project.project', string="Proyecto/Obra actual")
    location_name = fields.Char(string="Ubicación libre")
    serial_number = fields.Char(string="N° de serie o ID hardware")
    active = fields.Boolean(default=True, string="Activo")
