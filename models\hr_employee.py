from odoo import models, fields


class HrEmployee(models.Model):
    _inherit = 'hr.employee'

    x_dni = fields.Char(string="DNI", help="DNI que se usa para validar el control de acceso")
    x_fechaIngreso = fields.Date(string="Fecha de ingreso", help="Fecha de ingreso a la empresa")
    x_image_updated = fields.Boolean(string="No actualizar foto en el próximo ingreso/egreso",
                                     help="Sirve para actualizar la foto del empleado en Odoo tomando un acceso de asistencia")
